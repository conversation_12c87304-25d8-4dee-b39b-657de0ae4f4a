<script lang="ts">
	import { Timeline, TimelineItem, Badge, Card, Button, Toast, Tabs, TabItem } from 'flowbite-svelte';
	import {
		CheckCircleSolid,
		FileLinesSolid,
		DatabaseSolid,
		ServerSolid,
		RefreshOutline,
		ExclamationCircleOutline
	} from 'flowbite-svelte-icons';
	import { writable } from 'svelte/store';
	import { fly } from 'svelte/transition';
	import { onMount } from 'svelte';
	import { getBackendUrl } from '$src/lib/config';

	// Mock auth store for now - replace with actual auth implementation
	const authStore = writable({ token: 'mock-token' });

	// Workflow configuration types
	interface WorkflowStep {
		id: string;
		name: string;
		type: string;
		description: string;
		depends_on?: string[];
		config: {
			endpoint: string;
			method: string;
			headers?: Record<string, string>;
			request_body?: Record<string, any>;
			response_extraction?: Record<string, any>;
			validation?: {
				rules: Array<{
					id: string;
					type: string;
					path?: string;
					operator?: string;
					value?: string;
					error_message: string;
					warning_only?: boolean;
				}>;
			};
			retry?: {
				max_attempts: string | number;
				delay_seconds: string | number;
			};
		};
	}

	interface WorkflowConfiguration {
		schema_version: string;
		workflow: {
			id: string;
			name: string;
			version: string;
			description: string;
			category: string;
			tags: string[];
		};
		configuration: {
			api: {
				base_url: string;
				timeout_seconds: number;
				ssl_verify: boolean;
				max_retries: number;
				retry_delay_seconds: number;
				backoff_strategy: string;
				endpoints: Record<string, string>;
			};
			response_fields: Record<string, string>;
			validation: Record<string, string>;
			error_messages: Record<string, string>;
			data_source: {
				mode: string;
				fallback_mode: string;
				database_queries?: Array<any>;
				fixed_values: Record<string, string>;
			};
			execution: {
				timeout_minutes: number;
				retry_policy: {
					default_max_retries: number;
					default_delay_seconds: number;
					backoff_strategy: string;
				};
				cache: {
					enabled: boolean;
					duration_minutes: number;
					key_template: string;
					environments: Record<string, { duration_minutes: number }>;
				};
			};
			environments: Record<string, {
				api: {
					base_url: string;
					ssl_verify: boolean;
					timeout_seconds: number;
				};
				cache: { duration_minutes: number };
				credentials: {
					username: string;
					password: string;
				};
			}>;
		};
		steps: WorkflowStep[];
		validation: {
			input_schema: Record<string, any>;
			business_rules: Array<any>;
		};
		metadata: {
			support_contact: string;
		};
	}

	// Workflow data stores
	let policyListWorkflow: WorkflowConfiguration | null = null;
	let policyDetailsWorkflow: WorkflowConfiguration | null = null;
	let loading = true;
	let error: string | null = null;
	let baseUrl = getBackendUrl();

	// Function to fetch workflow configurations from backend
	async function fetchWorkflowConfigurations() {
		try {
			loading = true;
			error = null;

			const token = $authStore.token;
			if (!token) {
				throw new Error('Authentication token not found');
			}

			console.log('Fetching workflow configurations from:', `${baseUrl}/customer/api/policy-workflow-configurations/`);
      		const response = await fetch(`${baseUrl}/customer/api/policy-workflow-configurations/`, {

				method: 'GET',
				headers: {
					'Authorization': `Bearer ${token}`,
					'Content-Type': 'application/json'
				}
			});

			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}

			const data = await response.json();

			if (data.status === 'success') {
				policyListWorkflow = data.policy_list_workflow;
				policyDetailsWorkflow = data.policy_details_workflow;
			} else {
				throw new Error(data.error || 'Failed to load workflow configurations');
			}
		} catch (err) {
			console.error('Error fetching workflow configurations:', err);
			error = err instanceof Error ? err.message : 'Unknown error occurred';
		} finally {
			loading = false;
		}
	}

	// Load workflow configurations on component mount
	onMount(() => {
		fetchWorkflowConfigurations();
	});

	// Toast state
	let toastVisible = false;
	let toastMessage = '';
	let toastType: 'success' | 'error' = 'success';

	// Function to show toast notifications
	function showToast(message: string, type: 'success' | 'error') {
		toastMessage = message;
		toastType = type;
		toastVisible = true;
		setTimeout(() => {
			toastVisible = false;
		}, 3000);
	}
</script>

<div class="space-y-6 rounded-lg bg-white p-6 shadow-md">
	<div class="flex items-center justify-between">
		<div>
			<h2 class="text-xl font-medium text-gray-700">Policy & Claims API Integration</h2>
			<p class="text-sm text-gray-500">Workflow configurations and step-by-step process for fetching insurance policies and claims data</p>
		</div>
	</div>

	<!-- Loading State -->
	{#if loading}
		<div class="flex items-center justify-center p-8">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
			<span class="ml-3 text-gray-600">Loading workflow configurations...</span>
		</div>
	{:else if error}
		<div class="bg-red-50 border border-red-200 rounded-lg p-4">
			<div class="flex">
				<ExclamationCircleOutline class="h-5 w-5 text-red-400" />
				<div class="ml-3">
					<h3 class="text-sm font-medium text-red-800">Error Loading Workflows</h3>
					<div class="mt-2 text-sm text-red-700">
						<p>{error}</p>
					</div>
					<div class="mt-4">
						<Button color="red" size="sm" on:click={fetchWorkflowConfigurations}>
							<RefreshOutline class="w-4 h-4 mr-2" />
							Retry
						</Button>
					</div>
				</div>
			</div>
		</div>
	{:else}
		<!-- Tabbed Interface -->
		<Tabs tabStyle="pill" contentClass="p-4 bg-gray-50 rounded-lg dark:bg-gray-800 mt-4 ">
			<TabItem open title="Policy List Workflow">
				<svelte:fragment slot="title">
					Policy List Workflow
				</svelte:fragment>

				<div class="space-y-6">
					{#if policyListWorkflow}
						<!-- Workflow Header -->
						<div class="bg-white p-4 rounded-lg border">
							<h3 class="text-lg font-semibold text-gray-800 mb-2">{policyListWorkflow.workflow.name}</h3>
							<p class="text-sm text-gray-600 mb-4">{policyListWorkflow.workflow.description}</p>
							<div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
								<div>
									<Badge color="blue" class="mb-2">Version</Badge>
									<p class="text-gray-600">{policyListWorkflow.workflow.version}</p>
								</div>
								<div>
									<Badge color="green" class="mb-2">Timeout</Badge>
									<p class="text-gray-600">{policyListWorkflow.configuration.execution.timeout_minutes} minutes</p>
								</div>
								<div>
									<Badge color="purple" class="mb-2">Data Source</Badge>
									<p class="text-gray-600">{policyListWorkflow.configuration.data_source.mode}</p>
								</div>
							</div>
						</div>

						<!-- Configuration Details -->
						<Card size="xl">
							<h4 class="text-md font-medium text-gray-700 mb-3">Configuration Details</h4>
							<div class="space-y-4">
								<div>
									<Badge color="purple" class="mb-2">API Base URL</Badge>
									<code class="block bg-gray-100 p-2 rounded text-xs break-all">{policyListWorkflow.configuration.api.base_url}</code>
								</div>
								<div>
									<Badge color="indigo" class="mb-2">Fixed Values</Badge>
									<pre class="bg-gray-50 p-3 rounded text-xs overflow-x-auto"><code>{JSON.stringify(policyListWorkflow.configuration.data_source.fixed_values, null, 2)}</code></pre>
								</div>
								<div>
									<Badge color="yellow" class="mb-2">Cache Settings</Badge>
									<div class="text-sm text-gray-600">
										<p>Enabled: {policyListWorkflow.configuration.execution.cache.enabled ? 'Yes' : 'No'}</p>
										<p>Duration: {policyListWorkflow.configuration.execution.cache.duration_minutes} minutes</p>
									</div>
								</div>
							</div>
						</Card>

						<!-- Workflow Steps -->
						<div class="space-y-4">
							<h4 class="text-md font-medium text-gray-700">Workflow Steps</h4>
							<Timeline>
								{#each policyListWorkflow.steps as step, index}
									<TimelineItem title={step.name} date="Step {index + 1}">
										<svelte:component this={step.id === 'authenticate' ? ServerSolid : step.id === 'verify_citizen' ? CheckCircleSolid : step.id === 'verify_registration' ? CheckCircleSolid : FileLinesSolid} slot="icon" class="w-4 h-4 text-blue-600" />

										<Card class="mt-3" size="xl">
											<div class="space-y-4">
												<!-- Step Header -->
												<div>
													<h5 class="font-medium text-gray-800">{step.name}</h5>
													<p class="text-sm text-gray-600 mt-1">{step.description}</p>
													<div class="flex items-center space-x-2 mt-2">
														<Badge color="blue">{step.config.method}</Badge>
														{#if step.config.retry}
															<Badge color="dark">Max Retries: {step.config.retry.max_attempts}</Badge>
														{/if}
														{#if step.depends_on && step.depends_on.length > 0}
															<Badge color="yellow">Depends on: {step.depends_on.join(', ')}</Badge>
														{/if}
													</div>
												</div>

												<!-- Endpoint -->
												<div>
													<Badge color="dark" class="mb-2">Endpoint</Badge>
													<code class="block bg-gray-100 p-2 rounded text-xs break-all">{step.config.endpoint}</code>
												</div>

												<!-- Headers (if present) -->
												{#if step.config.headers}
													<div>
														<Badge color="yellow" class="mb-2">Headers</Badge>
														<pre class="bg-gray-50 p-3 rounded text-xs overflow-x-auto"><code>{JSON.stringify(step.config.headers, null, 2)}</code></pre>
													</div>
												{/if}

												<!-- Request Body -->
												{#if step.config.request_body}
													<div>
														<Badge color="purple" class="mb-2">Request Body</Badge>
														<pre class="bg-gray-50 p-3 rounded text-xs overflow-x-auto"><code>{JSON.stringify(step.config.request_body, null, 2)}</code></pre>
													</div>
												{/if}

												<!-- Response Extraction -->
												{#if step.config.response_extraction}
													<div>
														<Badge color="green" class="mb-2">Response Extraction</Badge>
														<pre class="bg-green-50 p-3 rounded text-xs overflow-x-auto"><code>{JSON.stringify(step.config.response_extraction, null, 2)}</code></pre>
													</div>
												{/if}

												<!-- Validation (if present) -->
												{#if step.config.validation}
													<div>
														<Badge color="red" class="mb-2">Validation Rules</Badge>
														<div class="space-y-2">
															{#each step.config.validation.rules as rule}
																<div class="bg-red-50 p-2 rounded text-xs">
																	<p><strong>Rule:</strong> {rule.id}</p>
																	<p><strong>Type:</strong> {rule.type}</p>
																	{#if rule.path}
																		<p><strong>Path:</strong> {rule.path}</p>
																	{/if}
																	{#if rule.operator}
																		<p><strong>Operator:</strong> {rule.operator}</p>
																	{/if}
																	{#if rule.value}
																		<p><strong>Value:</strong> {rule.value}</p>
																	{/if}
																	<p><strong>Error:</strong> {rule.error_message}</p>
																	{#if rule.warning_only}
																		<Badge color="yellow">Warning Only</Badge>
																	{/if}
																</div>
															{/each}
														</div>
													</div>
												{/if}
											</div>
										</Card>
									</TimelineItem>
								{/each}
							</Timeline>
						</div>
					{:else}
						<div class="text-center py-8">
							<p class="text-gray-500">Policy List Workflow configuration not available</p>
						</div>
					{/if}
				</div>

			</TabItem>

			<TabItem title="Policy Details Workflow">
				<svelte:fragment slot="title">
					Policy Details Workflow
				</svelte:fragment>

				<div class="space-y-6">
					{#if policyDetailsWorkflow}
						<!-- Workflow Header -->
						<div class="bg-white p-4 rounded-lg border">
							<h3 class="text-lg font-semibold text-gray-800 mb-2">{policyDetailsWorkflow.workflow.name}</h3>
							<p class="text-sm text-gray-600 mb-4">{policyDetailsWorkflow.workflow.description}</p>
							<div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
								<div>
									<Badge color="red" class="mb-2">Version</Badge>
									<p class="text-gray-600">{policyDetailsWorkflow.workflow.version}</p>
								</div>
								<div>
									<Badge color="green" class="mb-2">Timeout</Badge>
									<p class="text-gray-600">{policyDetailsWorkflow.configuration.execution.timeout_minutes} minutes</p>
								</div>
								<div>
									<Badge color="purple" class="mb-2">Data Source</Badge>
									<p class="text-gray-600">{policyDetailsWorkflow.configuration.data_source.mode}</p>
								</div>
							</div>
						</div>

						<!-- Configuration Details -->
						<Card size="xl">
							<h4 class="text-md font-medium text-gray-700 mb-3">Configuration Details</h4>
							<div class="space-y-4">
								<div>
									<Badge color="purple" class="mb-2">API Base URL</Badge>
									<code class="block bg-gray-100 p-2 rounded text-xs break-all">{policyDetailsWorkflow.configuration.api.base_url}</code>
								</div>
								<div>
									<Badge color="indigo" class="mb-2">Fixed Values</Badge>
									<pre class="bg-gray-50 p-3 rounded text-xs overflow-x-auto"><code>{JSON.stringify(policyDetailsWorkflow.configuration.data_source.fixed_values, null, 2)}</code></pre>
								</div>
								<div>
									<Badge color="yellow" class="mb-2">Cache Settings</Badge>
									<div class="text-sm text-gray-600">
										<p>Enabled: {policyDetailsWorkflow.configuration.execution.cache.enabled ? 'Yes' : 'No'}</p>
										<p>Duration: {policyDetailsWorkflow.configuration.execution.cache.duration_minutes} minutes</p>
									</div>
								</div>
							</div>
						</Card>

						<!-- Workflow Steps -->
						<div class="space-y-4">
							<h4 class="text-md font-medium text-gray-700">Workflow Steps</h4>
							<Timeline>
								{#each policyDetailsWorkflow.steps as step, index}
									<TimelineItem title={step.name} date="Step {index + 1}">
										<svelte:component this={step.id === 'authenticate' ? ServerSolid : step.id === 'fetch_policy_details' ? DatabaseSolid : CheckCircleSolid} slot="icon" class="w-4 h-4 text-red-600" />

										<Card class="mt-3" size="xl">
											<div class="space-y-4">
												<!-- Step Header -->
												<div>
													<h5 class="font-medium text-gray-800">{step.name}</h5>
													<p class="text-sm text-gray-600 mt-1">{step.description}</p>
													<div class="flex items-center space-x-2 mt-2">
														<Badge color="red">{step.config.method}</Badge>
														{#if step.config.retry}
															<Badge color="dark">Max Retries: {step.config.retry.max_attempts}</Badge>
														{/if}
														{#if step.depends_on && step.depends_on.length > 0}
															<Badge color="yellow">Depends on: {step.depends_on.join(', ')}</Badge>
														{/if}
													</div>
												</div>

												<!-- Endpoint -->
												<div>
													<Badge color="dark" class="mb-2">Endpoint</Badge>
													<code class="block bg-gray-100 p-2 rounded text-xs break-all">{step.config.endpoint}</code>
												</div>

												<!-- Headers (if present) -->
												{#if step.config.headers}
													<div>
														<Badge color="yellow" class="mb-2">Headers</Badge>
														<pre class="bg-gray-50 p-3 rounded text-xs overflow-x-auto"><code>{JSON.stringify(step.config.headers, null, 2)}</code></pre>
													</div>
												{/if}

												<!-- Request Body -->
												{#if step.config.request_body}
													<div>
														<Badge color="purple" class="mb-2">Request Body</Badge>
														<pre class="bg-gray-50 p-3 rounded text-xs overflow-x-auto"><code>{JSON.stringify(step.config.request_body, null, 2)}</code></pre>
													</div>
												{/if}

												<!-- Response Extraction -->
												{#if step.config.response_extraction}
													<div>
														<Badge color="green" class="mb-2">Response Extraction</Badge>
														<pre class="bg-green-50 p-3 rounded text-xs overflow-x-auto"><code>{JSON.stringify(step.config.response_extraction, null, 2)}</code></pre>
													</div>
												{/if}

												<!-- Validation (if present) -->
												{#if step.config.validation}
													<div>
														<Badge color="red" class="mb-2">Validation Rules</Badge>
														<div class="space-y-2">
															{#each step.config.validation.rules as rule}
																<div class="bg-red-50 p-2 rounded text-xs">
																	<p><strong>Rule:</strong> {rule.id}</p>
																	<p><strong>Type:</strong> {rule.type}</p>
																	{#if rule.path}
																		<p><strong>Path:</strong> {rule.path}</p>
																	{/if}
																	{#if rule.operator}
																		<p><strong>Operator:</strong> {rule.operator}</p>
																	{/if}
																	{#if rule.value}
																		<p><strong>Value:</strong> {rule.value}</p>
																	{/if}
																	<p><strong>Error:</strong> {rule.error_message}</p>
																	{#if rule.warning_only}
																		<Badge color="yellow">Warning Only</Badge>
																	{/if}
																</div>
															{/each}
														</div>
													</div>
												{/if}
											</div>
										</Card>
									</TimelineItem>
								{/each}
							</Timeline>
						</div>
					{:else}
						<div class="text-center py-8">
							<p class="text-gray-500">Policy Details Workflow configuration not available</p>
						</div>
					{/if}
				</div>

			</TabItem>
		</Tabs>
	{/if}

	<div class="mt-6 p-4 bg-blue-50 border-l-4 border-blue-400 rounded">
		<div class="flex">
			<div class="flex-shrink-0">
				<svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
					<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
				</svg>
			</div>
			<div class="ml-3">
				<h3 class="text-sm font-medium text-blue-800">Important Notes</h3>
				<div class="mt-2 text-sm text-blue-700">
					<ul class="list-disc list-inside space-y-1">
						<li>Authentication tokens have 1-hour expiry and can be reused for multiple API calls</li>
						<li>Customer must have a valid Citizen ID to proceed with policy fetching</li>
						<li>Each policy contains a unique MemberCode used for detailed data retrieval</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
</div>



<!-- Toast Notification -->
{#if toastVisible}
	<Toast
		color={toastType === 'success' ? 'green' : 'red'}
		position="top-right"
		transition={fly}
		params={{ x: 200 }}
		bind:toastStatus={toastVisible}
		class="fixed top-4 right-4 z-50"
	>
		<svelte:component this={toastType === 'success' ? CheckCircleSolid : ExclamationCircleOutline} slot="icon" class="w-5 h-5" />
		{toastMessage}
	</Toast>
{/if}
